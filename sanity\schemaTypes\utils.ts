import { Rule } from "sanity";

export const validatedArrayStringOrTextLength = (
  rule: Rule,
  {
    maxLength,
    minLength,
    fieldName,
  }: {
    maxLength: number;
    minLength?: number;
    fieldName: string;
  },
) => {
  return rule.custom((value) => {
    if (!value || !Array.isArray(value)) {
      return `${fieldName} é obrigatório`;
    }

    for (const item of value) {
      if (item && typeof item === "object" && "value" in item) {
        const textValue = item.value;
        if (typeof textValue === "string") {
          if (textValue.length > maxLength) {
            return `${fieldName} não pode exceder ${maxLength} caracteres`;
          }
          if (minLength && textValue.length < minLength) {
            return `${fieldName} deve ter pelo menos ${minLength} caracteres`;
          }
        }
      }
    }

    return true;
  });
};

/**
 * Validates that endTime is after startTime in activity objects
 */
export const validateActivityTimes = (rule: any) => {
  return rule.custom((activities: any[]) => {
    if (!activities || !Array.isArray(activities)) {
      return true; // Let other validations handle required/array checks
    }

    for (let i = 0; i < activities.length; i++) {
      const activity = activities[i];
      if (!activity || typeof activity !== "object") continue;

      const { startTime, endTime } = activity;

      if (startTime && endTime) {
        const startDate = new Date(startTime);
        const endDate = new Date(endTime);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return `Atividade ${i + 1}: Datas inválidas`;
        }

        if (endDate <= startDate) {
          return `Atividade ${i + 1}: Hora de fim deve ser posterior à hora de início`;
        }
      }
    }

    return true;
  });
};
